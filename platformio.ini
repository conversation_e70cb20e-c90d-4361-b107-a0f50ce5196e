[env:seeed_xiao_esp32s3]
platform = espressif32
board = seeed_xiao_esp32s3
framework = arduino
monitor_speed = 115200
upload_speed = 921600

; 库依赖
lib_deps = 
    bblanchon/Arduino<PERSON>son@^7.0.0
    me-no-dev/ESPAsyncWebServer@^1.2.3
    me-no-dev/AsyncTCP@^1.1.1

; 编译选项
build_flags = 
    -DCORE_DEBUG_LEVEL=3
    -DBOARD_HAS_PSRAM

; 分区表 (可选，如果需要更多存储空间)
; board_build.partitions = huge_app.csv


    liquidcs/esp32ARP@^1.0.0


