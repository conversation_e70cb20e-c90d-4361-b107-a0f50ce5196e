# 局域网设备扫描系统架构设计

## 1. 总体架构

本系统旨在为SeeedStudio XIAO ESP32系列（ESP32C3/ESP32S3）开发一个局域网设备扫描系统，能够自动发现网络中的设备IP和设备名，并通过网页界面显示所有设备状态。系统将采用C/C++语言在ESP32上实现，并提供一个基于Web的交互界面。

**系统主要组成部分：**
1.  **ESP32固件**: 运行在SeeedStudio XIAO ESP32设备上，负责设备扫描、数据处理和Web服务器功能。
2.  **Web前端**: 运行在用户的浏览器上，通过HTTP/WebSocket与ESP32固件通信，展示设备信息并提供交互。

```mermaid
graph TD
    A[用户浏览器] -->|HTTP/WebSocket| B(ESP32 Web服务器)
    B -->|API请求/数据更新| C{设备扫描与管理模块}
    C -->|mDNS/ARP/HTTP| D[局域网设备]
    C -->|数据存储| E[设备信息数据库/缓存]
```

## 2. 模块设计

### 2.1 设备扫描与管理模块 (ESP32固件)

该模块是系统的核心，负责发现、识别和管理局域网内的设备。

**主要功能：**
-   **设备发现**: 结合mDNS和ARP扫描两种方式。
    -   **mDNS扫描**: 主动查询网络中的mDNS服务，发现支持mDNS的设备并获取其主机名和IP地址。同时，ESP32自身也将作为mDNS服务发布者，方便其他mDNS客户端发现。
    -   **ARP扫描**: 遍历当前子网内的所有IP地址，发送ARP请求，通过ARP应答获取设备的MAC地址和IP地址。这将用于发现不支持mDNS的设备。
-   **设备信息解析**: 对发现的设备进行进一步的信息获取。
    -   对于mDNS发现的设备，直接获取其主机名和IP。
    -   对于ARP发现的设备，尝试通过HTTP请求（例如访问默认端口80）来判断是否为Web设备，并尝试获取其HTTP响应头中的Server信息，或通过其他协议（如NetBIOS）尝试获取设备名。
    -   未来可扩展支持更多协议（如SSDP、Bonjour等）以获取更丰富的设备信息。
-   **设备状态管理**: 维护一个设备列表，包含设备的IP地址、MAC地址、设备名（或主机名）、在线状态、上次活跃时间等信息。定期更新设备状态。
-   **数据存储/缓存**: 将发现的设备信息存储在ESP32的RAM中，或考虑使用SPIFFS/LittleFS进行持久化存储（对于设备名等可配置信息）。

### 2.2 Web服务器模块 (ESP32固件)

该模块负责处理来自Web前端的HTTP请求，并提供设备信息。

**主要功能：**
-   **HTTP服务器**: 搭建一个轻量级的HTTP服务器，用于提供前端网页文件（HTML, CSS, JavaScript）。
-   **RESTful API**: 提供API接口供前端调用，获取设备列表和设备状态。
    -   `GET /api/devices`: 返回当前发现的所有设备列表及其详细信息。
    -   `GET /api/device/{ip}`: 返回特定IP设备的详细信息。
    -   `POST /api/scan`: 触发一次手动设备扫描。
-   **WebSocket**: 提供WebSocket接口，实现设备状态的实时推送，避免前端频繁轮询。
    -   当设备状态发生变化（上线、下线、信息更新）时，通过WebSocket向所有连接的客户端推送更新。

### 2.3 前端网页界面 (用户浏览器)

该模块是用户与系统交互的界面，负责展示设备信息和提供操作。

**主要功能：**
-   **响应式布局**: 兼容不同尺寸的屏幕（PC、手机、平板）。
-   **设备列表展示**: 以表格或卡片形式展示发现的设备，包括IP地址、MAC地址、设备名、在线状态等。
-   **实时刷新**: 通过WebSocket接收ESP32推送的实时状态更新，动态更新设备列表。
-   **手动扫描**: 提供一个按钮，允许用户手动触发设备扫描。
-   **设备详情**: 点击设备可查看更详细的信息。
-   **搜索/过滤**: 提供搜索框和过滤选项，方便用户查找特定设备。

## 3. 技术选型

-   **开发框架**: ESP-IDF (推荐，提供更底层和灵活的控制) 或 Arduino (快速开发)。考虑到需要进行ARP扫描和更精细的网络控制，ESP-IDF可能更合适，但Arduino库的丰富性也值得考虑。最终选择将根据具体实现难度和资源限制决定。
-   **网络库**: ESP-IDF自带的TCP/IP协议栈和WiFi库，以及mDNS库。
-   **Web服务器**: ESP-IDF的HTTP Server组件或Arduino的ESPAsyncWebServer库。
-   **前端技术**: HTML5, CSS3, JavaScript (原生JS或轻量级框架如Vue.js/React的CDN版本，考虑到ESP32的存储和性能限制，原生JS或小型库更优)。
-   **数据格式**: JSON用于API接口的数据交换。

## 4. 性能考量

-   **扫描频率**: 考虑到网络负载和ESP32资源，扫描频率不宜过高。可以设置为每隔一定时间（如30秒-1分钟）进行一次全网扫描，或只在有新设备加入时触发。
-   **内存占用**: 优化设备信息存储结构，避免不必要的内存消耗。
-   **Web前端资源**: 尽量减小前端文件大小，减少ESP32的存储压力和传输带宽。
-   **并发连接**: Web服务器需要处理多个客户端的并发连接，合理配置连接数和缓冲区大小。

## 5. 安全性考量

-   **认证/授权**: 如果系统部署在公共网络，需要考虑Web界面的访问认证。
-   **输入验证**: 对所有来自前端的输入进行严格验证，防止注入攻击。
-   **固件更新**: 考虑OTA (Over-The-Air) 固件更新机制，方便后续维护和功能扩展。

## 6. 待解决问题/未来展望

-   如何高效地在ESP32上实现ARP扫描，并处理大量设备的响应？
-   如何获取除IP/MAC/主机名之外的更多设备信息（如设备类型、操作系统等）？
-   如何实现设备信息的持久化存储，并在重启后恢复？
-   考虑支持IPv6环境下的设备发现。

本设计文档为系统开发提供了初步的蓝图，具体实现细节将在后续阶段中逐步完善。

