/*
  SeeedStudio XIAO ESP32C3/ESP32S3 局域网设备扫描器
  功能：连接WiFi，发布mDNS服务，并扫描局域网内的设备。
*/

#include <WiFi.h>
#include <ESPmDNS.h>
#include <WebServer.h>
#include <ArduinoJson.h>

// WiFi 配置
const char* ssid = "your_SSID"; // 替换为你的WiFi名称
const char* password = "your_PASSWORD"; // 替换为你的WiFi密码

// Web服务器端口
const int webServerPort = 80;
WebServer server(webServerPort);

// 设备列表结构体
struct Device {
  String ip;
  String mac;
  String hostname;
  String status; // 例如："online", "offline"
  long lastSeen; // 上次看到的时间戳
};

// 存储发现的设备列表
// 考虑到ESP32的内存限制，这里使用固定大小的数组，实际应用中可能需要更动态的管理
#define MAX_DEVICES 50
Device devices[MAX_DEVICES];
int deviceCount = 0;

// 函数声明
void handleRoot();
void handleNotFound();
void setupWiFi();
void setupMDNS();
void setupWebServer();
void scanNetwork();
void addOrUpdateDevice(String ip, String mac, String hostname);
String getDeviceListJson();

void setup() {
  Serial.begin(115200);
  setupWiFi();
  setupMDNS();
  setupWebServer();

  Serial.println("\nESP32 Scanner Ready!");
  Serial.print("Access Web Server at: http://");
  Serial.print(WiFi.localIP());
  Serial.println("/");
}

void loop() {
  server.handleClient();
  MDNS.update();

  // 定期扫描网络，例如每30秒扫描一次
  static unsigned long lastScanTime = 0;
  if (millis() - lastScanTime > 30000) { // 30秒
    scanNetwork();
    lastScanTime = millis();
  }
}

void setupWiFi() {
  Serial.print("Connecting to WiFi: ");
  Serial.println(ssid);
  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println("\nWiFi connected");
  Serial.print("IP address: ");
  Serial.println(WiFi.localIP());
}

void setupMDNS() {
  if (!MDNS.begin("esp32_scanner")) { // 设置mDNS主机名
    Serial.println("Error setting up MDNS responder!");
    return;
  }
  Serial.println("mDNS responder started");
  // 添加HTTP服务，方便通过mDNS发现Web服务器
  MDNS.addService("http", "tcp", webServerPort);
}

void setupWebServer() {
  server.on("/", handleRoot);
  server.on("/api/devices", HTTP_GET, []() {
    server.send(200, "application/json", getDeviceListJson());
  });
  server.onNotFound(handleNotFound);
  server.begin();
  Serial.println("HTTP server started");
}

void handleRoot() {
  String html = "<!DOCTYPE html>\n";
  html += "<html>\n";
  html += "<head>\n";
  html += "<meta charset=\"UTF-8\">\n";
  html += "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n";
  html += "<title>ESP32 局域网设备扫描</title>\n";
  html += "<style>\n";
  html += "  body { font-family: Arial, sans-serif; margin: 20px; background-color: #f4f4f4; }\n";
  html += "  .container { background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }\n";
  html += "  h1 { color: #333; }\n";
  html += "  table { width: 100%; border-collapse: collapse; margin-top: 20px; }\n";
  html += "  th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n";
  html += "  th { background-color: #f2f2f2; }\n";
  html += "  .online { color: green; font-weight: bold; }\n";
  html += "  .offline { color: red; }\n";
  html += "</style>\n";
  html += "</head>\n";
  html += "<body>\n";
  html += "  <div class=\"container\">\n";
  html += "    <h1>ESP32 局域网设备扫描器</h1>\n";
  html += "    <p>当前IP: " + WiFi.localIP().toString() + "</p>\n";
  html += "    <table id=\"deviceTable\">\n";
  html += "      <thead>\n";
  html += "        <tr><th>IP 地址</th><th>MAC 地址</th><th>设备名</th><th>状态</th><th>上次活跃</th></tr>\n";
  html += "      </thead>\n";
  html += "      <tbody>\n";
  // 设备列表将由JavaScript动态加载
  html += "      </tbody>\n";
  html += "    </table>\n";
  html += "  </div>\n";
  html += "  <script>\n";
  html += "    function fetchDevices() {\n";
  html += "      fetch('/api/devices')\n";
  html += "        .then(response => response.json())\n";
  html += "        .then(data => {\n";
  html += "          const tableBody = document.querySelector('#deviceTable tbody');\n";
  html += "          tableBody.innerHTML = ''; // 清空现有内容\n";
  html += "          data.forEach(device => {\n";
  html += "            const row = tableBody.insertRow();\n";
  html += "            row.insertCell().textContent = device.ip;\n";
  html += "            row.insertCell().textContent = device.mac;\n";
  html += "            row.insertCell().textContent = device.hostname;\n";
  html += "            const statusCell = row.insertCell();\n";
  html += "            statusCell.textContent = device.status;\n";
  html += "            statusCell.className = device.status === 'online' ? 'online' : 'offline';\n";
  html += "            row.insertCell().textContent = new Date(device.lastSeen * 1000).toLocaleString();\n";
  html += "          });\n";
  html += "        })\n";
  html += "        .catch(error => console.error('Error fetching devices:', error));\n";
  html += "    }\n";
  html += "    setInterval(fetchDevices, 5000); // 每5秒刷新一次
  html += "    fetchDevices(); // 初始加载
  html += "  </script>\n";
  html += "</body>\n";
  html += "</html>\n";
  server.send(200, "text/html", html);
}

void handleNotFound() {
  server.send(404, "text/plain", "Not Found");
}

void scanNetwork() {
  Serial.println("\nStarting network scan...");

  // 1. mDNS 服务发现
  Serial.println("mDNS service discovery...");
  int n = MDNS.queryService("http", "tcp"); // 查找所有HTTP服务
  if (n == 0) {
    Serial.println("no mDNS services found");
  } else {
    Serial.print(n);
    Serial.println(" mDNS services found");
    for (int i = 0; i < n; ++i) {
      // 打印所有发现的服务信息
      Serial.print(i + 1);
      Serial.print(": ");
      Serial.print(MDNS.getHostname(i));
      Serial.print(" (");
      Serial.print(MDNS.IP(i));
      Serial.print("): ");
      Serial.print(MDNS.getPort(i));
      Serial.println("");
      addOrUpdateDevice(MDNS.IP(i).toString(), "Unknown", MDNS.getHostname(i));
    }
  }

  // 2. ARP 扫描 (简化实现，仅扫描当前子网)
  // 注意：Arduino框架下直接进行ARP扫描比较复杂，这里仅为示意，
  // 实际可能需要更底层的LwIP操作或使用专门的库。
  // 暂时只通过mDNS和WiFi.macAddress()来获取设备信息。
  // 真正的ARP扫描需要发送和解析ARP包，这超出了WebServer库的范畴。
  // 为了简化，这里假设我们只能通过mDNS和已连接设备的MAC地址来获取信息。
  // 后续如果需要更全面的ARP扫描，可能需要切换到ESP-IDF或寻找更专业的库。

  // 标记长时间未见的设备为离线
  unsigned long currentTime = millis() / 1000; // 秒
  for (int i = 0; i < deviceCount; i++) {
    if (currentTime - devices[i].lastSeen > 60) { // 60秒未见则标记为离线
      devices[i].status = "offline";
    }
  }
  Serial.println("Network scan finished.");
}

void addOrUpdateDevice(String ip, String mac, String hostname) {
  for (int i = 0; i < deviceCount; i++) {
    if (devices[i].ip == ip) {
      // 设备已存在，更新信息
      devices[i].mac = mac;
      devices[i].hostname = hostname;
      devices[i].status = "online";
      devices[i].lastSeen = millis() / 1000;
      Serial.print("Updated device: ");
      Serial.println(ip);
      return;
    }
  }

  // 新设备，添加到列表
  if (deviceCount < MAX_DEVICES) {
    devices[deviceCount].ip = ip;
    devices[deviceCount].mac = mac;
    devices[deviceCount].hostname = hostname;
    devices[deviceCount].status = "online";
    devices[deviceCount].lastSeen = millis() / 1000;
    deviceCount++;
    Serial.print("Added new device: ");
    Serial.println(ip);
  } else {
    Serial.println("Device list full, cannot add new device.");
  }
}

String getDeviceListJson() {
  DynamicJsonDocument doc(4096); // 足够大的JSON文档，根据MAX_DEVICES调整
  JsonArray data = doc.to<JsonArray>();

  for (int i = 0; i < deviceCount; i++) {
    JsonObject deviceObj = data.add<JsonObject>();
    deviceObj["ip"] = devices[i].ip;
    deviceObj["mac"] = devices[i].mac;
    deviceObj["hostname"] = devices[i].hostname;
    deviceObj["status"] = devices[i].status;
    deviceObj["lastSeen"] = devices[i].lastSeen;
  }

  String jsonString;
  serializeJson(doc, jsonString);
  return jsonString;
}


