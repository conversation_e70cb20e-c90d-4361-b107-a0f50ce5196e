/*
  SeeedStudio XIAO ESP32C3/ESP32S3 局域网设备扫描器
  功能：连接WiFi，发布mDNS服务，并扫描局域网内的设备。
*/

#include <WiFi.h>
#include <ESPmDNS.h>
#include <ArduinoJson.h>
#include <ESPAsyncWebServer.h>
#include <AsyncTCP.h>
#include "lwip/etharp.h"
#include "lwip/netif.h"

// WiFi 配置
const char* ssid = "your_SSID"; // 替换为你的WiFi名称
const char* password = "your_PASSWORD"; // 替换为你的WiFi密码

const int webServerPort = 80;

// 全局对象声明 - 必须在使用前声明
AsyncWebServer server(webServerPort);
AsyncWebSocket ws("/ws");


// 设备列表结构体
struct Device {
  String ip;
  String mac;
  String hostname;
  String status; // 例如："online", "offline"
  long lastSeen; // 上次看到的时间戳
};

// 存储发现的设备列表
// 考虑到ESP32的内存限制，这里使用固定大小的数组，实际应用中可能需要更动态的管理
#define MAX_DEVICES 50
Device devices[MAX_DEVICES];
int deviceCount = 0;

// 函数声明
void setupWiFi();
void setupMDNS();
void setupWebServer();
void scanNetwork();
void addOrUpdateDevice(String ip, String mac, String hostname);
String getDeviceListJson();
bool pingHost(IPAddress ip);
String getMacFromARP(IPAddress ip);

void setup() {
  Serial.begin(115200);
  setupWiFi();
  setupMDNS();
  setupWebServer();

  Serial.println("\nESP32 Scanner Ready!");
  Serial.print("Access Web Server at: http://");
  Serial.print(WiFi.localIP());
  Serial.println("/");
}

void loop() {
  // 检查WiFi连接状态，如果断开则尝试重连
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi connection lost, attempting to reconnect...");
    setupWiFi();
  }

  // 清理WebSocket连接
  ws.cleanupClients();

  // 更新mDNS
  MDNS.update();

  // 定期扫描网络，例如每30秒扫描一次
  static unsigned long lastScanTime = 0;
  if (millis() - lastScanTime > 30000) { // 30秒
    scanNetwork();
    lastScanTime = millis();
  }

  // 添加小延迟避免看门狗触发
  delay(10);
}

void setupWiFi() {
  Serial.print("Connecting to WiFi: ");
  Serial.println(ssid);
  WiFi.begin(ssid, password);

  // 添加超时机制，最多等待30秒
  unsigned long startTime = millis();
  const unsigned long timeout = 30000; // 30秒超时

  while (WiFi.status() != WL_CONNECTED && (millis() - startTime) < timeout) {
    delay(500);
    Serial.print(".");
  }

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\nWiFi connected");
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());
  } else {
    Serial.println("\nWiFi connection failed!");
    Serial.println("Please check your WiFi credentials and try again.");
    // 可以选择重启或进入AP模式
    ESP.restart();
  }
}

void setupMDNS() {
  if (!MDNS.begin("esp32_scanner")) { // 设置mDNS主机名
    Serial.println("Error setting up MDNS responder!");
    return;
  }
  Serial.println("mDNS responder started");
  // 添加HTTP服务，方便通过mDNS发现Web服务器
  MDNS.addService("http", "tcp", webServerPort);
}

void setupWebServer() {
  // WebSocket事件处理
  ws.onEvent([](AsyncWebSocket *server, AsyncWebSocketClient *client, AwsEventType type, void *arg, uint8_t *data, size_t len) {
    if (type == WS_EVT_CONNECT) {
      Serial.printf("WebSocket client #%u connected from %s\n", client->id(), client->remoteIP().toString().c_str());
      // 向新连接的客户端发送当前设备列表
      client->text(getDeviceListJson());
    } else if (type == WS_EVT_DISCONNECT) {
      Serial.printf("WebSocket client #%u disconnected\n", client->id());
    }
  });
  server.addHandler(&ws);

    server.on("/", HTTP_GET, [](AsyncWebServerRequest *request){
    String html = "<!DOCTYPE html>\n";
    html += "<html lang=\"zh-CN\">\n";
    html += "<head>\n";
    html += "<meta charset=\"UTF-8\">\n";
    html += "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n";
    html += "<title>ESP32 局域网设备扫描器</title>\n";
    html += "<style>\n";
    html += "  * { margin: 0; padding: 0; box-sizing: border-box; }\n";
    html += "  body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }\n";
    html += "  .container { max-width: 1200px; margin: 0 auto; background: rgba(255, 255, 255, 0.95); border-radius: 20px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); overflow: hidden; }\n";
    html += "  .header { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 30px; text-align: center; }\n";
    html += "  .header h1 { font-size: 2.5rem; margin-bottom: 10px; font-weight: 300; }\n";
    html += "  .header p { font-size: 1.1rem; opacity: 0.9; }\n";
    html += "  .controls { padding: 20px 30px; background: #f8f9fa; border-bottom: 1px solid #e9ecef; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px; }\n";
    html += "  .status-indicator { display: flex; align-items: center; gap: 10px; }\n";
    html += "  .status-dot { width: 12px; height: 12px; border-radius: 50%; animation: pulse 2s infinite; }\n";
    html += "  .status-dot.connected { background: #28a745; }\n";
    html += "  .status-dot.disconnected { background: #dc3545; }\n";
    html += "  @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }\n";
    html += "  .btn { padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; font-weight: 500; transition: all 0.3s ease; text-decoration: none; display: inline-block; }\n";
    html += "  .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }\n";
    html += "  .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4); }\n";
    html += "  .btn-secondary { background: #6c757d; color: white; }\n";
    html += "  .btn-secondary:hover { background: #5a6268; transform: translateY(-2px); }\n";
    html += "  .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; padding: 20px 30px; }\n";
    html += "  .stat-card { background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); text-align: center; transition: transform 0.3s ease; }\n";
    html += "  .stat-card:hover { transform: translateY(-5px); }\n";
    html += "  .stat-number { font-size: 2rem; font-weight: bold; color: #667eea; margin-bottom: 5px; }\n";
    html += "  .stat-label { color: #6c757d; font-size: 0.9rem; }\n";
    html += "  .table-container { padding: 0 30px 30px; }\n";
    html += "  .table-wrapper { background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n";
    html += "  table { width: 100%; border-collapse: collapse; }\n";
    html += "  th { background: #f8f9fa; padding: 15px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; }\n";
    html += "  td { padding: 15px; border-bottom: 1px solid #dee2e6; transition: background-color 0.3s ease; }\n";
    html += "  tr:hover td { background-color: #f8f9fa; }\n";
    html += "  .status-badge { padding: 4px 12px; border-radius: 20px; font-size: 0.8rem; font-weight: 500; text-transform: uppercase; }\n";
    html += "  .status-online { background: #d4edda; color: #155724; }\n";
    html += "  .status-offline { background: #f8d7da; color: #721c24; }\n";
    html += "  .device-ip { font-family: 'Courier New', monospace; font-weight: bold; color: #495057; }\n";
    html += "  .device-mac { font-family: 'Courier New', monospace; color: #6c757d; font-size: 0.9rem; }\n";
    html += "  .device-hostname { font-weight: 500; color: #495057; }\n";
    html += "  .empty-state { text-align: center; padding: 60px 20px; color: #6c757d; }\n";
    html += "  .empty-state-icon { font-size: 4rem; margin-bottom: 20px; opacity: 0.3; }\n";
    html += "  .loading { display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite; }\n";
    html += "  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }\n";
    html += "  @media (max-width: 768px) { .controls { flex-direction: column; align-items: stretch; } .stats { grid-template-columns: 1fr 1fr; } .header h1 { font-size: 2rem; } }\n";
    html += "</style>\n";
    html += "</head>\n";
    html += "<body>\n";
    html += "  <div class=\"container\">\n";
    html += "    <div class=\"header\">\n";
    html += "      <h1>🌐 ESP32 局域网设备扫描器</h1>\n";
    html += "      <p>实时监控您的网络设备状态</p>\n";
    html += "    </div>\n";
    html += "    \n";
    html += "    <div class=\"controls\">\n";
    html += "      <div class=\"status-indicator\">\n";
    html += "        <div id=\"statusDot\" class=\"status-dot disconnected\"></div>\n";
    html += "        <span id=\"wsStatus\">WebSocket: 连接中...</span>\n";
    html += "      </div>\n";
    html += "      <div>\n";
    html += "        <button class=\"btn btn-primary\" onclick=\"manualScan()\" id=\"scanBtn\">\n";
    html += "          <span id=\"scanText\">🔍 手动扫描</span>\n";
    html += "        </button>\n";
    html += "        <button class=\"btn btn-secondary\" onclick=\"toggleAutoScan()\" id=\"autoScanBtn\">\n";
    html += "          <span id=\"autoScanText\">⏱️ 自动扫描: 开启</span>\n";
    html += "        </button>\n";
    html += "      </div>\n";
    html += "    </div>\n";
    html += "    \n";
    html += "    <div class=\"stats\">\n";
    html += "      <div class=\"stat-card\">\n";
    html += "        <div class=\"stat-number\" id=\"totalDevices\">0</div>\n";
    html += "        <div class=\"stat-label\">总设备数</div>\n";
    html += "      </div>\n";
    html += "      <div class=\"stat-card\">\n";
    html += "        <div class=\"stat-number\" id=\"onlineDevices\">0</div>\n";
    html += "        <div class=\"stat-label\">在线设备</div>\n";
    html += "      </div>\n";
    html += "      <div class=\"stat-card\">\n";
    html += "        <div class=\"stat-number\" id=\"offlineDevices\">0</div>\n";
    html += "        <div class=\"stat-label\">离线设备</div>\n";
    html += "      </div>\n";
    html += "      <div class=\"stat-card\">\n";
    html += "        <div class=\"stat-number\" id=\"lastScanTime\">--:--</div>\n";
    html += "        <div class=\"stat-label\">上次扫描</div>\n";
    html += "      </div>\n";
    html += "    </div>\n";
    html += "    \n";
    html += "    <div class=\"table-container\">\n";
    html += "      <div class=\"table-wrapper\">\n";
    html += "        <table id=\"deviceTable\">\n";
    html += "          <thead>\n";
    html += "            <tr>\n";
    html += "              <th>IP 地址</th>\n";
    html += "              <th>MAC 地址</th>\n";
    html += "              <th>设备名称</th>\n";
    html += "              <th>状态</th>\n";
    html += "              <th>上次活跃时间</th>\n";
    html += "            </tr>\n";
    html += "          </thead>\n";
    html += "          <tbody id=\"deviceTableBody\">\n";
    html += "            <tr>\n";
    html += "              <td colspan=\"5\" class=\"empty-state\">\n";
    html += "                <div class=\"empty-state-icon\">📡</div>\n";
    html += "                <div>正在扫描网络设备...</div>\n";
    html += "              </td>\n";
    html += "            </tr>\n";
    html += "          </tbody>\n";
    html += "        </table>\n";
    html += "      </div>\n";
    html += "    </div>\n";
    html += "  </div>\n";
    html += "  \n";
    html += "  <script>\n";
    html += "    let ws;\n";
    html += "    let autoScanEnabled = true;\n";
    html += "    let deviceData = [];\n";
    html += "    \n";
    html += "    function connectWebSocket() {\n";
    html += "      const wsStatus = document.getElementById('wsStatus');\n";
    html += "      const statusDot = document.getElementById('statusDot');\n";
    html += "      \n";
    html += "      ws = new WebSocket('ws://' + window.location.host + '/ws');\n";
    html += "      \n";
    html += "      ws.onopen = function() {\n";
    html += "        wsStatus.textContent = 'WebSocket: 已连接';\n";
    html += "        statusDot.className = 'status-dot connected';\n";
    html += "      };\n";
    html += "      \n";
    html += "      ws.onmessage = function(event) {\n";
    html += "        try {\n";
    html += "          deviceData = JSON.parse(event.data);\n";
    html += "          updateDeviceTable(deviceData);\n";
    html += "          updateStats(deviceData);\n";
    html += "          updateLastScanTime();\n";
    html += "        } catch (error) {\n";
    html += "          console.error('Error parsing WebSocket data:', error);\n";
    html += "        }\n";
    html += "      };\n";
    html += "      \n";
    html += "      ws.onclose = function() {\n";
    html += "        wsStatus.textContent = 'WebSocket: 连接断开';\n";
    html += "        statusDot.className = 'status-dot disconnected';\n";
    html += "        setTimeout(connectWebSocket, 3000);\n";
    html += "      };\n";
    html += "      \n";
    html += "      ws.onerror = function(error) {\n";
    html += "        console.error('WebSocket error:', error);\n";
    html += "      };\n";
    html += "    }\n";
    html += "    \n";
    html += "    function updateDeviceTable(data) {\n";
    html += "      const tableBody = document.getElementById('deviceTableBody');\n";
    html += "      \n";
    html += "      if (data.length === 0) {\n";
    html += "        tableBody.innerHTML = `\n";
    html += "          <tr>\n";
    html += "            <td colspan=\"5\" class=\"empty-state\">\n";
    html += "              <div class=\"empty-state-icon\">🔍</div>\n";
    html += "              <div>未发现任何设备</div>\n";
    html += "            </td>\n";
    html += "          </tr>\n";
    html += "        `;\n";
    html += "        return;\n";
    html += "      }\n";
    html += "      \n";
    html += "      tableBody.innerHTML = '';\n";
    html += "      data.forEach(device => {\n";
    html += "        const row = tableBody.insertRow();\n";
    html += "        row.innerHTML = `\n";
    html += "          <td class=\"device-ip\">${device.ip}</td>\n";
    html += "          <td class=\"device-mac\">${device.mac}</td>\n";
    html += "          <td class=\"device-hostname\">${device.hostname || '未知设备'}</td>\n";
    html += "          <td><span class=\"status-badge status-${device.status}\">${device.status === 'online' ? '在线' : '离线'}</span></td>\n";
    html += "          <td>${new Date(device.lastSeen * 1000).toLocaleString('zh-CN')}</td>\n";
    html += "        `;\n";
    html += "      });\n";
    html += "    }\n";
    html += "    \n";
    html += "    function updateStats(data) {\n";
    html += "      const totalDevices = data.length;\n";
    html += "      const onlineDevices = data.filter(d => d.status === 'online').length;\n";
    html += "      const offlineDevices = totalDevices - onlineDevices;\n";
    html += "      \n";
    html += "      document.getElementById('totalDevices').textContent = totalDevices;\n";
    html += "      document.getElementById('onlineDevices').textContent = onlineDevices;\n";
    html += "      document.getElementById('offlineDevices').textContent = offlineDevices;\n";
    html += "    }\n";
    html += "    \n";
    html += "    function updateLastScanTime() {\n";
    html += "      const now = new Date();\n";
    html += "      document.getElementById('lastScanTime').textContent = now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });\n";
    html += "    }\n";
    html += "    \n";
    html += "    function manualScan() {\n";
    html += "      const scanBtn = document.getElementById('scanBtn');\n";
    html += "      const scanText = document.getElementById('scanText');\n";
    html += "      \n";
    html += "      scanBtn.disabled = true;\n";
    html += "      scanText.innerHTML = '<div class=\"loading\"></div> 扫描中...';\n";
    html += "      \n";
    html += "      fetch('/api/scan', { method: 'POST' })\n";
    html += "        .then(response => response.json())\n";
    html += "        .then(data => {\n";
    html += "          console.log('Manual scan triggered:', data);\n";
    html += "          setTimeout(() => {\n";
    html += "            scanBtn.disabled = false;\n";
    html += "            scanText.innerHTML = '🔍 手动扫描';\n";
    html += "          }, 3000);\n";
    html += "        })\n";
    html += "        .catch(error => {\n";
    html += "          console.error('Error triggering scan:', error);\n";
    html += "          scanBtn.disabled = false;\n";
    html += "          scanText.innerHTML = '🔍 手动扫描';\n";
    html += "        });\n";
    html += "    }\n";
    html += "    \n";
    html += "    function toggleAutoScan() {\n";
    html += "      autoScanEnabled = !autoScanEnabled;\n";
    html += "      const autoScanText = document.getElementById('autoScanText');\n";
    html += "      autoScanText.textContent = `⏱️ 自动扫描: ${autoScanEnabled ? '开启' : '关闭'}`;\n";
    html += "    }\n";
    html += "    \n";
    html += "    // 初始化\n";
    html += "    connectWebSocket();\n";
    html += "  </script>\n";
    html += "</body>\n";
    html += "</html>\n";
    request->send(200, "text/html", html);
  });

  server.on("/api/devices", HTTP_GET, [](AsyncWebServerRequest *request){
    request->send(200, "application/json", getDeviceListJson());
  });

  server.on("/api/scan", HTTP_POST, [](AsyncWebServerRequest *request){
    // 触发手动扫描
    scanNetwork();
    request->send(200, "application/json", "{\"status\":\"scan_triggered\"}");
  });

  server.onNotFound([](AsyncWebServerRequest *request){
    request->send(404, "text/plain", "Not Found");
  });

  server.begin();
  Serial.println("HTTP server started");
}

void scanNetwork() {
  Serial.println("\nStarting network scan...");

  // 1. mDNS 服务发现
  Serial.println("mDNS service discovery...");
  int n = MDNS.queryService("http", "tcp"); // 查找所有HTTP服务
  if (n == 0) {
    Serial.println("no mDNS services found");
  } else {
    Serial.print(n);
    Serial.println(" mDNS services found");
    for (int i = 0; i < n; ++i) {
      // 打印所有发现的服务信息
      Serial.print(i + 1);
      Serial.print(": ");
      Serial.print(MDNS.getHostname(i));
      Serial.print(" (");
      Serial.print(MDNS.IP(i));
      Serial.print("): ");
      Serial.print(MDNS.getPort(i));
      Serial.println("");
      addOrUpdateDevice(MDNS.IP(i).toString(), "Unknown", MDNS.getHostname(i));
    }
  }

  // 2. 网络扫描 (简化版本，使用ping检测)
  Serial.println("Network scanning...");
  IPAddress localIP = WiFi.localIP();
  IPAddress subnet = WiFi.subnetMask();

  // 计算网络地址和广播地址
  uint32_t networkAddr = (uint32_t)localIP & (uint32_t)subnet;
  uint32_t broadcastAddr = networkAddr | (~(uint32_t)subnet);

  Serial.printf("Scanning network: %s/%d\n",
                IPAddress(networkAddr).toString().c_str(),
                __builtin_popcount((uint32_t)subnet));

  // 限制扫描范围以避免超时（只扫描前50个地址）
  uint32_t maxScan = min(networkAddr + 50, broadcastAddr);

  for (uint32_t addr = networkAddr + 1; addr < maxScan; addr++) {
    IPAddress scanIP(addr);

    // 避免扫描自身IP
    if (scanIP == localIP) {
      continue;
    }

    // 使用ping检测设备是否在线
    if (pingHost(scanIP)) {
      String macStr = getMacFromARP(scanIP);
      Serial.printf("Found device: IP %s\n", scanIP.toString().c_str());
      addOrUpdateDevice(scanIP.toString(), macStr, "Unknown");
    }

    // 添加小延迟避免网络拥塞
    delay(50);
  }

  // 标记长时间未见的设备为离线
  unsigned long currentTime = millis() / 1000; // 秒
  for (int i = 0; i < deviceCount; i++) {
    if (currentTime - devices[i].lastSeen > 60) { // 60秒未见则标记为离线
      devices[i].status = "offline";
    }
  }
    Serial.println("Network scan finished.");
  
  // 通过WebSocket向所有连接的客户端广播更新的设备列表
  ws.textAll(getDeviceListJson());
}

void addOrUpdateDevice(String ip, String mac, String hostname) {
  // 验证输入参数
  if (ip.length() == 0) {
    Serial.println("Warning: Empty IP address, skipping device");
    return;
  }

  // 查找现有设备
  for (int i = 0; i < deviceCount; i++) {
    if (devices[i].ip == ip) {
      // 设备已存在，更新信息
      if (mac != "Unknown" && mac.length() > 0) {
        devices[i].mac = mac;
      }
      if (hostname != "Unknown" && hostname.length() > 0) {
        devices[i].hostname = hostname;
      }
      devices[i].status = "online";
      devices[i].lastSeen = millis() / 1000;
      Serial.printf("Updated device: %s (%s)\n", ip.c_str(), hostname.c_str());
      return;
    }
  }

  // 新设备，添加到列表
  if (deviceCount < MAX_DEVICES) {
    devices[deviceCount].ip = ip;
    devices[deviceCount].mac = (mac.length() > 0) ? mac : "Unknown";
    devices[deviceCount].hostname = (hostname.length() > 0) ? hostname : "Unknown";
    devices[deviceCount].status = "online";
    devices[deviceCount].lastSeen = millis() / 1000;
    deviceCount++;
    Serial.printf("Added new device: %s (%s)\n", ip.c_str(), devices[deviceCount-1].hostname.c_str());
  } else {
    Serial.println("Warning: Device list full, cannot add new device.");
  }
}

String getDeviceListJson() {
  // 动态计算所需的JSON文档大小
  size_t estimatedSize = 200; // 基础大小
  for (int i = 0; i < deviceCount; i++) {
    estimatedSize += devices[i].ip.length() + devices[i].mac.length() +
                     devices[i].hostname.length() + devices[i].status.length() + 100;
  }

  // 确保有足够的缓冲区，但不超过8KB
  size_t docSize = min(estimatedSize * 2, (size_t)8192);
  DynamicJsonDocument doc(docSize);

  JsonArray data = doc.to<JsonArray>();

  for (int i = 0; i < deviceCount; i++) {
    JsonObject deviceObj = data.add<JsonObject>();

    // 检查内存是否足够
    if (doc.overflowed()) {
      Serial.println("Warning: JSON document overflow, truncating device list");
      break;
    }

    deviceObj["ip"] = devices[i].ip;
    deviceObj["mac"] = devices[i].mac;
    deviceObj["hostname"] = devices[i].hostname;
    deviceObj["status"] = devices[i].status;
    deviceObj["lastSeen"] = devices[i].lastSeen;
  }

  String jsonString;
  if (serializeJson(doc, jsonString) == 0) {
    Serial.println("Error: Failed to serialize JSON");
    return "[]"; // 返回空数组
  }

  return jsonString;
}

// 简单的ping功能来检测设备是否在线
bool pingHost(IPAddress ip) {
  WiFiClient client;
  client.setTimeout(1000); // 1秒超时

  // 尝试连接到常见端口来检测设备
  int ports[] = {80, 443, 22, 23, 21, 25, 53, 110, 143, 993, 995};
  int numPorts = sizeof(ports) / sizeof(ports[0]);

  for (int i = 0; i < numPorts; i++) {
    if (client.connect(ip, ports[i])) {
      client.stop();
      return true;
    }
  }
  return false;
}

// 从ARP表获取MAC地址的简化实现
String getMacFromARP(IPAddress ip) {
  // 注意：这是一个简化的实现
  // 在实际应用中，可能需要更复杂的ARP表查询
  return "Unknown";
}



AsyncWebServer server(webServerPort);
AsyncWebSocket ws("/ws");

