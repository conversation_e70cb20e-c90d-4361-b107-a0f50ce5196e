#!/usr/bin/env python3
"""
简单的PlatformIO编译测试脚本
用于验证项目是否可以正常编译
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """运行命令并返回结果"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            return True
        else:
            print(f"❌ {description} 失败")
            print(f"错误输出: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} 超时")
        return False
    except Exception as e:
        print(f"❌ {description} 异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 ESP32 局域网设备扫描器 - 编译测试")
    print("=" * 50)
    
    # 检查是否在项目根目录
    if not os.path.exists("platformio.ini"):
        print("❌ 错误: 未找到 platformio.ini 文件")
        print("请确保在项目根目录下运行此脚本")
        sys.exit(1)
    
    # 检查PlatformIO是否安装
    if not run_command("pio --version", "检查PlatformIO安装"):
        print("❌ PlatformIO未安装或不在PATH中")
        print("请先安装PlatformIO: https://platformio.org/install")
        sys.exit(1)
    
    # 清理项目
    run_command("pio run --target clean", "清理项目")
    
    # 编译项目
    if run_command("pio run", "编译项目"):
        print("\n🎉 编译成功！项目代码没有语法错误。")
        print("\n📝 下一步:")
        print("1. 修改 main.cpp 中的 WiFi 凭据")
        print("2. 连接 ESP32S3 开发板")
        print("3. 运行: pio run --target upload")
    else:
        print("\n💥 编译失败！请检查代码错误。")
        sys.exit(1)

if __name__ == "__main__":
    main()
