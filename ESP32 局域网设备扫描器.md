# ESP32 局域网设备扫描器

一个基于SeeedStudio XIAO ESP32S3的局域网设备扫描系统，能够自动发现网络中的设备并通过现代化Web界面实时显示设备状态。

## 🌟 主要特性

- **自动设备发现**：结合mDNS和ARP扫描技术，全面发现局域网设备
- **实时状态监控**：WebSocket实时推送设备状态变化
- **现代化界面**：响应式设计，支持桌面和移动设备
- **低功耗运行**：基于ESP32S3，支持24/7连续运行
- **即插即用**：无需额外服务器，ESP32独立运行

## 🛠 硬件要求

- SeeedStudio XIAO ESP32S3开发板
- USB-C数据线
- 2.4GHz WiFi网络

## 🚀 快速开始

### 1. 环境准备

- 安装 [Visual Studio Code](https://code.visualstudio.com/)
- 安装 PlatformIO 插件

### 2. 配置项目

1. 克隆或下载项目代码
2. 在VS Code中打开项目文件夹
3. 修改 `src/main.cpp` 中的WiFi凭据：
   ```cpp
   const char* ssid = "your_SSID";
   const char* password = "your_PASSWORD";
   ```

### 3. 编译上传

1. 点击VS Code底部的 "PlatformIO: Build" 编译项目
2. 连接ESP32S3到电脑
3. 点击 "PlatformIO: Upload" 上传固件

### 4. 访问界面

1. 打开串口监视器查看ESP32的IP地址
2. 在浏览器中访问该IP地址
3. 享受实时的网络设备监控！

## 📱 界面预览

- **统计仪表板**：显示总设备数、在线/离线设备统计
- **实时设备列表**：IP地址、MAC地址、设备名称、状态
- **WebSocket状态指示**：实时连接状态显示
- **手动扫描**：支持立即触发网络扫描

## 🔧 技术架构

- **硬件平台**：ESP32S3 (双核240MHz, 8MB Flash, 8MB PSRAM)
- **网络扫描**：mDNS服务发现 + ARP扫描
- **Web服务器**：ESPAsyncWebServer (异步架构)
- **实时通信**：WebSocket
- **前端技术**：HTML5 + CSS3 + JavaScript (原生)

## 📚 详细文档

查看 [完整文档](ESP32_Network_Scanner_Documentation.md) 了解：
- 详细安装指南
- 技术原理解析
- 故障排除方法
- 功能扩展建议

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [ESP32 Arduino Core](https://github.com/espressif/arduino-esp32)
- [ESPAsyncWebServer](https://github.com/me-no-dev/ESPAsyncWebServer)
- [ArduinoJson](https://github.com/bblanchon/ArduinoJson)
- [esp32ARP](https://github.com/liquidCS/esp32ARP)

---

**由 Manus AI 开发** 🤖

